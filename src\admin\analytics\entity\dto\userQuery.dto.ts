import {
  IsOptional,
  IsNotEmpty,
  IsDate,
  IsString,
  <PERSON>N<PERSON>ber,
  Min,
  Max,
  IsIn,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class UserQueryDto {
  @IsNotEmpty()
  @IsDate()
  @Transform(({ value }) => (value ? new Date(value) : value))
  startDate: Date;

  @IsNotEmpty()
  @IsDate()
  @Transform(({ value }) => (value ? new Date(value) : value))
  endDate: Date;

  @IsOptional()
  @IsString()
  @IsIn(['Male', 'Female', 'MALE', 'FEMALE'], {
    message: 'Gender must be one of Male, Female, MALE, or FEMALE',
  })
  gender?: string;

  @IsOptional()
  @IsString()
  state?: string;

  // CIBIL Score (CibilScoreEntity)
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(300)
  @Max(900)
  startCibil?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(300)
  @Max(900)
  endCibil?: number;

  // PL Score (CibilScoreEntity)
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(300)
  @Max(900)
  startPl?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(300)
  @Max(900)
  endPl?: number;

  // Age (KYCEntity.aadhaarDOB se calculate)
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  startAge?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  endAge?: number;

  // Salary (CibilScoreEntity.monthlyIncome)
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  startSalary?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  endSalary?: number;
}
