import { Controller, Get, Query } from '@nestjs/common';
import { AnalyticsService } from './analytics.service';
import { UserQueryDto } from './entity/dto/userQuery.dto';

@Controller('analytics')
export class AnalyticsController {
  constructor(private readonly service: AnalyticsService) {}

  // Dashboard API
  @Get()
  async getDashboard() {
    return await this.service.getDashboard();
  }

  // User Stage Count
  @Get('stage')
  getUserStage(@Query() query: UserQueryDto) {
    return this.service.getUserStage(query);
  }

  // Registered User count
  @Get('register')
  async getRegisteredUserData(@Query() query: UserQueryDto) {
    return this.service.getRegister(query);
  }

  @Get('registerStageCount')
  getUserStageCount(@Query() query: UserQueryDto) {
    return this.service.userAnalytics(query);
  }
}
