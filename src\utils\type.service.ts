import { Injectable } from '@nestjs/common';

@Injectable()
export class CommonService {
  getUTCDateRange(date1: string, date2: string) {
    const kUTCTrail = 'T18:30:00.000Z';
    const dateA = this.getGlobalDate(new Date(date1));
    dateA.setDate(dateA.getDate() - 1);
    const startDate = dateA.toJSON().substring(0, 10) + kUTCTrail;
    const dateB = this.getGlobalDate(new Date(date2));
    dateA.setDate(dateB.getDate() - 1);
    const endDate = dateB.toJSON().substring(0, 10) + kUTCTrail;

    return { startDate, endDate };
  }

  getGlobalDate(experimentDate: Date) {
    try {
      const currentDate = new Date(experimentDate);
      currentDate.setMinutes(currentDate.getMinutes() + 330);
      const currentStatic =
        currentDate.toJSON().substring(0, 10) + 'T10:00:00.000Z';

      return new Date(currentStatic);
    } catch (error) {
      return experimentDate;
    }
  }

  toPascalCase(input: string) {
    return input
      .split('_')
      .map((word) => {
        if (word.toLowerCase() === 'ios') return 'IOS';
        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
      })
      .join(' ');
  }
}
