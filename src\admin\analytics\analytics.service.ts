import { Injectable } from '@nestjs/common';
import { Op } from 'sequelize';
import { NUMBERS, UserStage } from 'src/constant/objects';
import { CommonService } from 'src/utils/type.service';
import { PgService } from 'src/database/pg/pg.service';
import { registeredUsers } from 'src/database/pg/entities/registeredUsers';
import { RedisService } from 'src/database/redis/redis.service';
// import { v4 as uuidv4 } from 'uuid';

import {
  // FILTER_TYPES,
  QUERY_GRAPH_FILTERS,
  QUERY_GRAPHS,
  REG_USERS_COUNT,
  STAGE_COUNT,
} from 'src/constant/global';
import { ClickHouseService } from 'src/database/clickhouse/clickhouse.service';
@Injectable()
export class AnalyticsService {
  constructor(
    private readonly commonService: CommonService,
    private readonly pgService: PgService,
    private readonly redisService: RedisService,
    private readonly ClickHouseService: ClickHouseService,
  ) {}

  //#region Dashboard
  async getDashboard(query) {
    const [graphs, filters] = await Promise.all([
      this.ClickHouseService.injectQuery(QUERY_GRAPHS),
      this.ClickHouseService.injectQuery(QUERY_GRAPH_FILTERS),
    ]);

    const filtersArray = filters as any[];
    const stage = await this.getUserStage(query);
    const register = await this.getRegister(query);
    // return { filters: filters, graphs };
    return {
      filters: filtersArray,
      graphs: [
        { ...graphs[0], data: stage },
        { ...graphs[1], data: register },
      ],
    };
  }
  //#endregion

  //#region User Analytics
  async userAnalytics(
    query,
  ): Promise<{ stageSummary: any[]; registrationSummary: any[] }> {
    const cacheKey = this.createDynamicRedisKey(query, 'ANALYTICS');
    const today = new Date().toISOString().slice(0, 10);
    const queryDate = query?.startDate
      ? new Date(query.startDate).toISOString().slice(0, 10)
      : today;

    const hasFilters = this.hasFilters(query);

    // 1. Try to fetch from ClickHouse for non-today dates without filters
    if (queryDate !== today && !hasFilters) {
      const clickHouseData = await this.getClickHouseData(query);

      const hasStageData = clickHouseData.stageSummary?.some(
        (item) => Number(item.yKey) > 0,
      );
      const hasRegData = clickHouseData.registrationSummary?.some(
        (item) => Number(item.yKey) > 0,
      );

      if (hasStageData && hasRegData) {
        console.log('ClickHouse data found:', clickHouseData);
        return clickHouseData;
      }
      // fallback to Postgres if no data in ClickHouse
    }

    // 2. get cached result from Redis for today's date
    if (queryDate === today) {
      const cachedData = await this.redisService.get(cacheKey);
      if (cachedData) {
        console.log('Redis cache hit:', cacheKey);
        return JSON.parse(cachedData).result;
      }
      // cache miss, fallback to Postgres
    }

    // 3. Build where filter and fetch data from Postgres
    const where = await this.buildWhereFilter(query);

    const rows = await this.pgService.findAll(registeredUsers, {
      attributes: ['stage', 'gender', 'typeOfDevice', 'completedLoans'],
      where,
      raw: true,
    });

    console.log('Postgres rows count:', rows.length, 'Filter:', where);

    // 4. Throw if no data found to avoid unnecessary processing
    if (!rows || rows.length === 0) {
      throw new Error('No data found for the given query.');
    }

    // 5. Process stage summary aggregation
    const stageCounts: Record<string, number> = {};
    for (const key in UserStage) {
      stageCounts[key] = 0;
    }

    for (const row of rows) {
      const stageKey = Object.keys(UserStage).find(
        (k) => UserStage[k] === row.stage,
      );
      if (stageKey) {
        stageCounts[stageKey]++;
      }
    }

    const stageSummary = Object.entries(stageCounts).map(([xKey, yKey]) => ({
      xKey: this.commonService.toPascalCase(xKey),
      yKey,
    }));

    // 6. Process registration summary aggregation
    let registerCount = 0,
      male = 0,
      female = 0,
      android = 0,
      ios = 0,
      web = 0,
      newUsers = 0,
      repeatUsers = 0;

    for (const { gender, typeOfDevice, completedLoans } of rows) {
      registerCount++;

      if (gender === 'MALE') male++;
      else if (gender === 'FEMALE') female++;

      if (typeOfDevice === '0') android++;
      else if (typeOfDevice === '1') ios++;
      else if (typeOfDevice === '2') web++;

      if (completedLoans === 0) newUsers++;
      else if (completedLoans > 0) repeatUsers++;
    }

    const registrationSummary = [
      { xKey: 'Registration Count', yKey: registerCount },
      { xKey: 'Male Registration', yKey: male },
      { xKey: 'Female Registration', yKey: female },
      { xKey: 'Android Users', yKey: android },
      { xKey: 'IOS Users', yKey: ios },
      { xKey: 'Web Users', yKey: web },
      { xKey: 'New Users', yKey: newUsers },
      { xKey: 'Repeat Users', yKey: repeatUsers },
    ];

    // 7. Insert data into ClickHouse or cache in Redis as appropriate
    if (queryDate !== today && !hasFilters) {
      const sRowObj = this.formatSummary(stageSummary, queryDate);
      const regRowObj = this.formatSummary(registrationSummary, queryDate);

      await Promise.all([
        this.ClickHouseService.insertToClickhouse(
          'UsersStageAnalytics',
          sRowObj,
        ),
        this.ClickHouseService.insertToClickhouse('RegisteredUsers', regRowObj),
      ]);
    } else if (queryDate === today) {
      await this.redisService.set(
        cacheKey,
        JSON.stringify({ result: { stageSummary, registrationSummary } }),
        NUMBERS.THREE_HOURS_IN_SECONDS,
      );
      console.log('Redis cache set:', cacheKey);
    }

    return { stageSummary, registrationSummary };
  }

  //#endregion

  // stage summary
  async getUserStage(query) {
    const { stageSummary } = await this.userAnalytics(query);
    console.log('Stage Summary---?getUserStage//', stageSummary);
    return stageSummary;
  }

  // registration summary
  async getRegister(query) {
    const result = await this.userAnalytics(query);
    console.log(
      'Registration Summary----getRegister??',
      result.registrationSummary,
    );
    return result.registrationSummary;
  }

  private hasFilters(query): boolean {
    return !!(
      query?.startCibil ||
      query?.endCibil ||
      query?.startPl ||
      query?.endPl ||
      query?.startSalary ||
      query?.endSalary ||
      query?.startAge ||
      query?.endAge ||
      query?.gender ||
      query?.state
    );
  }
  //#endregion

  //#region getClickHouseData
  async getClickHouseData(query) {
    const { startDate, endDate } = this.commonService.getUTCDateRange(
      query.startDate,
      query.endDate,
    );
    const start = startDate.split('T')[0];
    const end = endDate.split('T')[0];
    const regQuery = REG_USERS_COUNT.replace('{{START_DATE}}', start).replace(
      '{{END_DATE}}',
      end,
    );

    const stageQuery = STAGE_COUNT.replace('{{START_DATE}}', start).replace(
      '{{END_DATE}}',
      end,
    );

    const [regData, stageData] = await Promise.all([
      this.ClickHouseService.injectQuery(regQuery),
      this.ClickHouseService.injectQuery(stageQuery),
    ]);

    if (!regData || !stageData) {
      throw new Error('No data found from clickhouse.');
    }
    const stageSummary = this.processClickHouseData(stageData);
    const registrationSummary = this.processClickHouseData(regData);

    return { stageSummary, registrationSummary };
  }
  //#region

  private processClickHouseData(data) {
    const summary = {};
    for (const row of data) {
      for (const key in row) {
        if (key !== 'graph_date' && key !== 'id') {
          const xKey = this.commonService.toPascalCase(key);
          summary[xKey] = (summary[xKey] ?? 0) + Number(row[key]);
        }
      }
    }
    return Object.entries(summary).map(([xKey, yKey]) => ({ xKey, yKey }));
  }

  private formatSummary(
    summaryArray: Array<{ xKey: string; yKey: any }>,
    queryDate: string | Date,
  ) {
    // Convert Date object to YYYY-MM-DD string format for ClickHouse
    const formattedDate =
      queryDate instanceof Date
        ? queryDate.toISOString().slice(0, 10)
        : queryDate;

    return Object.assign(
      { graph_date: formattedDate },
      ...summaryArray.map((item) => ({
        [item.xKey.replace(/\s+/g, '_').toLowerCase()]: item.yKey,
      })),
    );
  }

  //#region Filter Analytics
  private async buildWhereFilter(query) {
    const { startDate, endDate } = this.commonService.getUTCDateRange(
      query.startDate,
      query.endDate,
    );
    const where: any = {};

    const startCibil = query?.startCibil;
    const endCibil = query?.endCibil;
    const startPl = query?.startPl;
    const endPl = query?.endPl;
    const startSalary = query?.startSalary;
    const endSalary = query?.endSalary;
    const startAge = query?.startAge;
    const endAge = query?.endAge;

    if (startDate && endDate) {
      where.createdAt = { [Op.gte]: startDate, [Op.lte]: endDate };
    }
    if (query?.gender) where.gender = query?.gender;
    if (query?.state) where.state = query?.state;

    let userIds: string[] | null = null;

    // CIBIL/PL/Salary filter
    if (
      startCibil ||
      endCibil ||
      startPl ||
      endPl ||
      startSalary ||
      endSalary
    ) {
      const scoreWhere: any = {};

      if (startCibil || endCibil) {
        scoreWhere.cibilScore = {};
        if (startCibil) scoreWhere.cibilScore[Op.gte] = startCibil;
        if (endCibil) scoreWhere.cibilScore[Op.lte] = endCibil;
      }
      if (startPl || endPl) {
        scoreWhere.plScore = {};
        if (startPl) scoreWhere.plScore[Op.gte] = startPl;
        if (endPl) scoreWhere.plScore[Op.lte] = endPl;
      }
      if (startSalary || endSalary) {
        scoreWhere.monthlyIncome = {};
        if (startSalary) scoreWhere.monthlyIncome[Op.gte] = startSalary;
        if (endSalary) scoreWhere.monthlyIncome[Op.lte] = endSalary;
      }

      const cibilUsers = await this.pgService.findAll('CibilScoreEntity', {
        attributes: ['userId'],
        where: scoreWhere,
        raw: true,
      });

      userIds = cibilUsers.map((u) => u.userId);
    }

    // Age filter
    if (startAge || endAge) {
      const kycUsers = await this.pgService.findAll('KYCEntity', {
        attributes: ['userId', 'aadhaarDOB'],
        where: { aadhaarDOB: { [Op.ne]: null, [Op.not]: '' } },
        raw: true,
      });

      const currentYear = new Date().getFullYear();
      const ageUserIds = kycUsers
        .filter(({ aadhaarDOB }) => {
          let year: number | null = null;
          if (/^\d{2}\/\d{2}\/\d{4}$/.test(aadhaarDOB))
            year = parseInt(aadhaarDOB.split('/')[2], 10);
          else if (/^\d{2}-\d{2}-\d{4}$/.test(aadhaarDOB))
            year = parseInt(aadhaarDOB.split('-')[2], 10);
          else if (/^\d{4}-\d{2}-\d{2}$/.test(aadhaarDOB))
            year = parseInt(aadhaarDOB.split('-')[0], 10);

          if (!year) return false;
          const age = currentYear - year;
          if (startAge && age < startAge) return false;
          if (endAge && age > endAge) return false;
          return true;
        })
        .map((u) => u.userId);

      userIds = userIds
        ? userIds.filter((id) => ageUserIds.includes(id))
        : ageUserIds;
    }

    if (userIds !== null) {
      where.id = userIds.length > 0 ? { [Op.in]: userIds } : null;
    }

    return where;
  }
  //#endregion

  //#region createDynamicRedisKey
  private createDynamicRedisKey(query, baseKey: string): string {
    const normalized = {
      gender: query.gender ? query?.gender.toUpperCase() : null,
      state: query.state ? query?.state.toUpperCase() : null,
      age: {
        start: query?.startAge ?? '',
        end: query?.endAge ?? '',
      },
      cibil: {
        start: query?.startCibil ?? '',
        end: query?.endCibil ?? '',
      },
      pl: {
        start: query?.startPl ?? '',
        end: query?.endPl ?? '',
      },
      salary: {
        start: query?.startSalary ?? '',
        end: query?.endSalary ?? '',
      },
    };
    return `${baseKey}:${JSON.stringify(normalized)}`;
  }
  //#endregion
}
